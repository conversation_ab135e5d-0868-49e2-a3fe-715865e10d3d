'use client';

import { useState } from 'react';
import { BarChart3, TrendingUp, Users, Search, Clock, Target } from 'lucide-react';
import { AppState } from '@/types';

interface AnalyticsViewProps {
  appState: AppState;
  updateAppState: (updates: Partial<AppState>) => void;
}

export default function AnalyticsView({ appState, updateAppState }: AnalyticsViewProps) {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');

  // Mock analytics data
  const analyticsData = {
    totalContacts: appState.allContacts.length,
    totalQueries: 47,
    averageMatchScore: 0.78,
    topIndustries: [
      { name: 'Grocery Retail', count: 156 },
      { name: 'Food Service', count: 89 },
      { name: 'Manufacturing', count: 67 },
      { name: 'Technology', count: 45 },
      { name: 'Healthcare', count: 34 },
    ],
    topLocations: [
      { name: 'Toronto, ON', count: 89 },
      { name: 'Vancouver, BC', count: 67 },
      { name: 'Montreal, QC', count: 56 },
      { name: 'Calgary, AB', count: 45 },
      { name: 'Ottawa, ON', count: 34 },
    ],
    queryHistory: [
      { query: 'category managers grocery chains', timestamp: new Date('2024-01-15'), resultCount: 23 },
      { query: 'bakeries Alberta', timestamp: new Date('2024-01-14'), resultCount: 15 },
      { query: 'food service directors', timestamp: new Date('2024-01-13'), resultCount: 31 },
      { query: 'retail managers Toronto', timestamp: new Date('2024-01-12'), resultCount: 18 },
      { query: 'procurement specialists', timestamp: new Date('2024-01-11'), resultCount: 27 },
    ],
    performanceMetrics: {
      averageQueryTime: 1.2,
      successRate: 0.94,
      userSatisfaction: 0.87,
    },
    usageStats: {
      dailyQueries: [12, 15, 8, 23, 19, 16, 21],
      weeklyUploads: [3, 1, 2, 4, 2, 1, 3],
    }
  };

  const StatCard = ({ icon: Icon, title, value, subtitle, color }: {
    icon: any;
    title: string;
    value: string | number;
    subtitle?: string;
    color: string;
  }) => (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          <Icon size={24} className="text-white" />
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">Analytics Dashboard</h2>
            <p className="text-sm text-gray-600">
              Insights into your contact filtering performance
            </p>
          </div>
          
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          icon={Users}
          title="Total Contacts"
          value={analyticsData.totalContacts.toLocaleString()}
          subtitle="Across all files"
          color="bg-blue-500"
        />
        <StatCard
          icon={Search}
          title="Total Queries"
          value={analyticsData.totalQueries}
          subtitle={`${timeRange} period`}
          color="bg-green-500"
        />
        <StatCard
          icon={Target}
          title="Avg Match Score"
          value={`${(analyticsData.averageMatchScore * 100).toFixed(1)}%`}
          subtitle="Query accuracy"
          color="bg-purple-500"
        />
        <StatCard
          icon={Clock}
          title="Avg Query Time"
          value={`${analyticsData.performanceMetrics.averageQueryTime}s`}
          subtitle="Response speed"
          color="bg-orange-500"
        />
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Success Rate</h3>
          <div className="flex items-center justify-center">
            <div className="relative w-32 h-32">
              <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#e5e7eb"
                  strokeWidth="3"
                />
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#10b981"
                  strokeWidth="3"
                  strokeDasharray={`${analyticsData.performanceMetrics.successRate * 100}, 100`}
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-2xl font-bold text-gray-900">
                  {(analyticsData.performanceMetrics.successRate * 100).toFixed(0)}%
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">User Satisfaction</h3>
          <div className="flex items-center justify-center">
            <div className="relative w-32 h-32">
              <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#e5e7eb"
                  strokeWidth="3"
                />
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#3b82f6"
                  strokeWidth="3"
                  strokeDasharray={`${analyticsData.performanceMetrics.userSatisfaction * 100}, 100`}
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-2xl font-bold text-gray-900">
                  {(analyticsData.performanceMetrics.userSatisfaction * 100).toFixed(0)}%
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Daily Queries</h3>
          <div className="space-y-2">
            {analyticsData.usageStats.dailyQueries.map((queries, index) => (
              <div key={index} className="flex items-center gap-3">
                <span className="text-sm text-gray-600 w-8">
                  {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'][index]}
                </span>
                <div className="flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full"
                    style={{ width: `${(queries / Math.max(...analyticsData.usageStats.dailyQueries)) * 100}%` }}
                  />
                </div>
                <span className="text-sm font-medium text-gray-900 w-6">{queries}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Top Industries and Locations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Top Industries</h3>
          <div className="space-y-3">
            {analyticsData.topIndustries.map((industry, index) => (
              <div key={industry.name} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <span className="text-sm font-medium text-gray-600">#{index + 1}</span>
                  <span className="text-sm text-gray-900">{industry.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-20 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full"
                      style={{ 
                        width: `${(industry.count / Math.max(...analyticsData.topIndustries.map(i => i.count))) * 100}%` 
                      }}
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-8">{industry.count}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Top Locations</h3>
          <div className="space-y-3">
            {analyticsData.topLocations.map((location, index) => (
              <div key={location.name} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <span className="text-sm font-medium text-gray-600">#{index + 1}</span>
                  <span className="text-sm text-gray-900">{location.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-20 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full"
                      style={{ 
                        width: `${(location.count / Math.max(...analyticsData.topLocations.map(l => l.count))) * 100}%` 
                      }}
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-8">{location.count}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Query History */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Recent Query History</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left py-2 text-gray-600">Query</th>
                <th className="text-left py-2 text-gray-600">Date</th>
                <th className="text-left py-2 text-gray-600">Results</th>
                <th className="text-left py-2 text-gray-600">Performance</th>
              </tr>
            </thead>
            <tbody>
              {analyticsData.queryHistory.map((query, index) => (
                <tr key={index} className="border-b hover:bg-gray-50">
                  <td className="py-3 font-medium text-gray-900">{query.query}</td>
                  <td className="py-3 text-gray-600">{query.timestamp.toLocaleDateString()}</td>
                  <td className="py-3 text-gray-600">{query.resultCount} contacts</td>
                  <td className="py-3">
                    <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                      Excellent
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
