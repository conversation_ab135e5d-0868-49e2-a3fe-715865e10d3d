'use client';

import { useState } from 'react';
import { Settings, Save, Refresh<PERSON><PERSON>, Key, Sliders, Palette } from 'lucide-react';
import { AppState, AIConfig } from '@/types';

interface SettingsPanelProps {
  appState: AppState;
  updateAppState: (updates: Partial<AppState>) => void;
}

export default function SettingsPanel({ appState, updateAppState }: SettingsPanelProps) {
  const [config, setConfig] = useState<AIConfig>({
    provider: 'openai',
    apiKey: '',
    model: 'gpt-4',
    temperature: 0.3,
    maxTokens: 2000,
  });

  const [matchingSettings, setMatchingSettings] = useState({
    fuzzyThreshold: 0.7,
    semanticThreshold: 0.8,
    maxResults: 50,
  });

  const [uiSettings, setUISettings] = useState({
    theme: 'light' as 'light' | 'dark' | 'auto',
    language: 'en',
  });

  const [showApiKey, setShowApi<PERSON><PERSON>] = useState(false);

  const handleSaveSettings = () => {
    // In a real app, this would save to localStorage or backend
    console.log('Saving settings:', { config, matchingSettings, uiSettings });
    
    // Show success message
    updateAppState({
      error: null, // Clear any existing errors
    });
    
    // You could show a toast notification here
    alert('Settings saved successfully!');
  };

  const handleTestConnection = async () => {
    if (!config.apiKey) {
      alert('Please enter an API key first');
      return;
    }

    // Simulate API test
    try {
      updateAppState({ isProcessing: true });
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert('Connection test successful!');
    } catch (error) {
      alert('Connection test failed. Please check your API key and settings.');
    } finally {
      updateAppState({ isProcessing: false });
    }
  };

  return (
    <div className="space-y-6">
      {/* AI Configuration */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center gap-2 mb-4">
          <Key className="text-blue-500" size={20} />
          <h2 className="text-xl font-semibold text-gray-800">AI Configuration</h2>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              AI Provider
            </label>
            <select
              value={config.provider}
              onChange={(e) => setConfig(prev => ({ ...prev, provider: e.target.value as any }))}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="openai">OpenAI</option>
              <option value="anthropic">Anthropic</option>
              <option value="local">Local Model</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Model
            </label>
            <select
              value={config.model}
              onChange={(e) => setConfig(prev => ({ ...prev, model: e.target.value }))}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {config.provider === 'openai' && (
                <>
                  <option value="gpt-4">GPT-4</option>
                  <option value="gpt-4-turbo">GPT-4 Turbo</option>
                  <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                </>
              )}
              {config.provider === 'anthropic' && (
                <>
                  <option value="claude-3-opus">Claude 3 Opus</option>
                  <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                  <option value="claude-3-haiku">Claude 3 Haiku</option>
                </>
              )}
              {config.provider === 'local' && (
                <option value="llama-2">Llama 2</option>
              )}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              API Key
            </label>
            <div className="relative">
              <input
                type={showApiKey ? 'text' : 'password'}
                value={config.apiKey}
                onChange={(e) => setConfig(prev => ({ ...prev, apiKey: e.target.value }))}
                placeholder="Enter your API key"
                className="w-full border border-gray-300 rounded-lg px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                type="button"
                onClick={() => setShowApiKey(!showApiKey)}
                className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
              >
                {showApiKey ? '🙈' : '👁️'}
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Temperature: {config.temperature}
              </label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={config.temperature}
                onChange={(e) => setConfig(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>Focused</span>
                <span>Creative</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Tokens
              </label>
              <input
                type="number"
                value={config.maxTokens}
                onChange={(e) => setConfig(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}
                min="100"
                max="4000"
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <button
            onClick={handleTestConnection}
            disabled={appState.isProcessing}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <RefreshCw className={appState.isProcessing ? 'animate-spin' : ''} size={16} />
            Test Connection
          </button>
        </div>
      </div>

      {/* Matching Settings */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center gap-2 mb-4">
          <Sliders className="text-green-500" size={20} />
          <h2 className="text-xl font-semibold text-gray-800">Matching Settings</h2>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Fuzzy Match Threshold: {matchingSettings.fuzzyThreshold}
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={matchingSettings.fuzzyThreshold}
              onChange={(e) => setMatchingSettings(prev => ({ 
                ...prev, 
                fuzzyThreshold: parseFloat(e.target.value) 
              }))}
              className="w-full"
            />
            <p className="text-xs text-gray-500 mt-1">
              Lower values include more approximate matches
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Semantic Threshold: {matchingSettings.semanticThreshold}
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={matchingSettings.semanticThreshold}
              onChange={(e) => setMatchingSettings(prev => ({ 
                ...prev, 
                semanticThreshold: parseFloat(e.target.value) 
              }))}
              className="w-full"
            />
            <p className="text-xs text-gray-500 mt-1">
              Minimum confidence for semantic similarity matches
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Maximum Results
            </label>
            <input
              type="number"
              value={matchingSettings.maxResults}
              onChange={(e) => setMatchingSettings(prev => ({ 
                ...prev, 
                maxResults: parseInt(e.target.value) 
              }))}
              min="10"
              max="1000"
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* UI Settings */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center gap-2 mb-4">
          <Palette className="text-purple-500" size={20} />
          <h2 className="text-xl font-semibold text-gray-800">Interface Settings</h2>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Theme
            </label>
            <select
              value={uiSettings.theme}
              onChange={(e) => setUISettings(prev => ({ ...prev, theme: e.target.value as any }))}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="auto">Auto (System)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Language
            </label>
            <select
              value={uiSettings.language}
              onChange={(e) => setUISettings(prev => ({ ...prev, language: e.target.value }))}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="en">English</option>
              <option value="fr">Français</option>
              <option value="es">Español</option>
            </select>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={handleSaveSettings}
          className="flex items-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
        >
          <Save size={16} />
          Save All Settings
        </button>
      </div>
    </div>
  );
}
