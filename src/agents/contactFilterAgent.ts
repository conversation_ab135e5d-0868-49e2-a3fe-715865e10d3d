import { StateGraph, END } from '@langchain/langgraph';
import { AgentState, ExtractedEntities, SearchStrategy, MatchResult, Contact } from '@/types';
import { fuzzyMatch, semanticMatch, exactMatch } from '@/lib/matching';
import { extractEntities } from '@/lib/entityExtraction';

// Agent node functions
export class ContactFilterAgent {
  private graph: StateGraph<AgentState>;

  constructor() {
    this.graph = new StateGraph<AgentState>({
      channels: {
        query: { value: '' },
        extractedEntities: { 
          value: { companies: [], jobTitles: [], locations: [], industries: [], keywords: [] } 
        },
        searchStrategy: { 
          value: { type: 'combined', fields: [], weights: {}, threshold: 0.7 } 
        },
        results: { value: [] },
        confidence: { value: 0 },
        step: { value: 'parsing' },
      }
    });

    this.buildGraph();
  }

  private buildGraph() {
    // Add nodes
    this.graph.addNode('parse_query', this.parseQuery.bind(this));
    this.graph.addNode('extract_entities', this.extractEntitiesNode.bind(this));
    this.graph.addNode('generate_strategy', this.generateStrategy.bind(this));
    this.graph.addNode('search_contacts', this.searchContacts.bind(this));
    this.graph.addNode('rank_results', this.rankResults.bind(this));

    // Add edges
    this.graph.addEdge('parse_query', 'extract_entities');
    this.graph.addEdge('extract_entities', 'generate_strategy');
    this.graph.addEdge('generate_strategy', 'search_contacts');
    this.graph.addEdge('search_contacts', 'rank_results');
    this.graph.addEdge('rank_results', END);

    // Set entry point
    this.graph.setEntryPoint('parse_query');
  }

  private async parseQuery(state: AgentState): Promise<Partial<AgentState>> {
    console.log('Parsing query:', state.query);
    
    // Basic query preprocessing
    const cleanedQuery = state.query.toLowerCase().trim();
    
    return {
      query: cleanedQuery,
      step: 'entity_extraction',
    };
  }

  private async extractEntitiesNode(state: AgentState): Promise<Partial<AgentState>> {
    console.log('Extracting entities from:', state.query);
    
    const entities = await extractEntities(state.query);
    
    return {
      extractedEntities: entities,
      step: 'strategy_generation',
    };
  }

  private async generateStrategy(state: AgentState): Promise<Partial<AgentState>> {
    console.log('Generating search strategy for entities:', state.extractedEntities);
    
    const strategy: SearchStrategy = {
      type: 'combined',
      fields: this.determineSearchFields(state.extractedEntities),
      weights: this.calculateFieldWeights(state.extractedEntities),
      threshold: 0.7,
    };
    
    return {
      searchStrategy: strategy,
      step: 'searching',
    };
  }

  private async searchContacts(state: AgentState): Promise<Partial<AgentState>> {
    console.log('Searching contacts with strategy:', state.searchStrategy);
    
    // This would be called with actual contact data
    // For now, return empty results
    const results: MatchResult[] = [];
    
    return {
      results,
      step: 'ranking',
    };
  }

  private async rankResults(state: AgentState): Promise<Partial<AgentState>> {
    console.log('Ranking results:', state.results.length);
    
    // Sort by score descending
    const rankedResults = [...state.results].sort((a, b) => b.score - a.score);
    
    // Calculate overall confidence
    const confidence = rankedResults.length > 0 
      ? rankedResults.reduce((sum, r) => sum + r.score, 0) / rankedResults.length
      : 0;
    
    return {
      results: rankedResults,
      confidence,
      step: 'complete',
    };
  }

  private determineSearchFields(entities: ExtractedEntities): string[] {
    const fields: string[] = [];
    
    if (entities.companies.length > 0) fields.push('company');
    if (entities.jobTitles.length > 0) fields.push('title');
    if (entities.locations.length > 0) fields.push('city', 'state', 'address');
    if (entities.industries.length > 0) fields.push('industry');
    if (entities.keywords.length > 0) fields.push('name', 'notes');
    
    // Default fields if no specific entities found
    if (fields.length === 0) {
      fields.push('name', 'company', 'title', 'industry');
    }
    
    return fields;
  }

  private calculateFieldWeights(entities: ExtractedEntities): Record<string, number> {
    const weights: Record<string, number> = {};
    
    // Higher weights for fields with extracted entities
    if (entities.companies.length > 0) weights.company = 1.0;
    if (entities.jobTitles.length > 0) weights.title = 0.9;
    if (entities.locations.length > 0) {
      weights.city = 0.8;
      weights.state = 0.8;
    }
    if (entities.industries.length > 0) weights.industry = 0.7;
    
    // Default weights
    weights.name = weights.name || 0.6;
    weights.email = weights.email || 0.5;
    
    return weights;
  }

  public async processQuery(query: string, contacts: Contact[]): Promise<MatchResult[]> {
    const initialState: AgentState = {
      query,
      extractedEntities: { companies: [], jobTitles: [], locations: [], industries: [], keywords: [] },
      searchStrategy: { type: 'combined', fields: [], weights: {}, threshold: 0.7 },
      results: [],
      confidence: 0,
      step: 'parsing',
    };

    try {
      // Run the graph
      const compiledGraph = this.graph.compile();
      const finalState = await compiledGraph.invoke(initialState);
      
      // Perform actual search with contacts
      const searchResults = await this.performSearch(finalState, contacts);
      
      return searchResults;
    } catch (error) {
      console.error('Error processing query:', error);
      return [];
    }
  }

  private async performSearch(state: AgentState, contacts: Contact[]): Promise<MatchResult[]> {
    const results: MatchResult[] = [];
    
    for (const contact of contacts) {
      const matches = await this.matchContact(contact, state);
      if (matches.score >= state.searchStrategy.threshold) {
        results.push(matches);
      }
    }
    
    // Sort by score and limit results
    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, 50); // Limit to top 50 results
  }

  private async matchContact(contact: Contact, state: AgentState): Promise<MatchResult> {
    const { extractedEntities, searchStrategy } = state;
    let totalScore = 0;
    let matchCount = 0;
    const matchedFields: string[] = [];
    const explanations: string[] = [];

    // Check each search field
    for (const field of searchStrategy.fields) {
      const fieldValue = contact[field]?.toString().toLowerCase() || '';
      const weight = searchStrategy.weights[field] || 0.5;
      
      let fieldScore = 0;
      let fieldMatched = false;

      // Check against extracted entities
      if (field === 'company' && extractedEntities.companies.length > 0) {
        for (const company of extractedEntities.companies) {
          const score = fuzzyMatch(fieldValue, company.toLowerCase());
          if (score > fieldScore) {
            fieldScore = score;
            if (score > 0.7) fieldMatched = true;
          }
        }
      }

      if (field === 'title' && extractedEntities.jobTitles.length > 0) {
        for (const title of extractedEntities.jobTitles) {
          const score = fuzzyMatch(fieldValue, title.toLowerCase());
          if (score > fieldScore) {
            fieldScore = score;
            if (score > 0.7) fieldMatched = true;
          }
        }
      }

      if ((field === 'city' || field === 'state') && extractedEntities.locations.length > 0) {
        for (const location of extractedEntities.locations) {
          const score = fuzzyMatch(fieldValue, location.toLowerCase());
          if (score > fieldScore) {
            fieldScore = score;
            if (score > 0.7) fieldMatched = true;
          }
        }
      }

      if (field === 'industry' && extractedEntities.industries.length > 0) {
        for (const industry of extractedEntities.industries) {
          const score = fuzzyMatch(fieldValue, industry.toLowerCase());
          if (score > fieldScore) {
            fieldScore = score;
            if (score > 0.7) fieldMatched = true;
          }
        }
      }

      // Check against general keywords
      if (extractedEntities.keywords.length > 0) {
        for (const keyword of extractedEntities.keywords) {
          if (fieldValue.includes(keyword.toLowerCase())) {
            fieldScore = Math.max(fieldScore, 0.8);
            fieldMatched = true;
          }
        }
      }

      if (fieldMatched) {
        matchedFields.push(field);
        explanations.push(`${field}: ${(fieldScore * 100).toFixed(0)}% match`);
      }

      totalScore += fieldScore * weight;
      matchCount++;
    }

    const finalScore = matchCount > 0 ? totalScore / matchCount : 0;
    const explanation = explanations.length > 0 
      ? explanations.join(', ')
      : 'No specific matches found';

    return {
      contact,
      score: finalScore,
      matchedFields,
      explanation,
    };
  }
}
