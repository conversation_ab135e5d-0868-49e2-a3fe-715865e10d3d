import { ExtractedEntities, SearchStrategy, MatchResult, Contact } from '@/types';
import { fuzzyMatch } from '@/lib/matching';

// Simplified agent implementation
export class ContactFilterAgent {
  constructor() {
    // Simple constructor
  }

  private determineSearchFields(entities: ExtractedEntities): string[] {
    const fields: string[] = [];

    if (entities.companies.length > 0) fields.push('company');
    if (entities.jobTitles.length > 0) fields.push('title');
    if (entities.locations.length > 0) fields.push('city', 'state', 'address');
    if (entities.industries.length > 0) fields.push('industry');
    if (entities.keywords.length > 0) fields.push('name', 'notes');

    // Default fields if no specific entities found
    if (fields.length === 0) {
      fields.push('name', 'company', 'title', 'industry');
    }

    return fields;
  }

  private calculateFieldWeights(entities: ExtractedEntities): Record<string, number> {
    const weights: Record<string, number> = {};

    // Higher weights for fields with extracted entities
    if (entities.companies.length > 0) weights.company = 1.0;
    if (entities.jobTitles.length > 0) weights.title = 0.9;
    if (entities.locations.length > 0) {
      weights.city = 0.8;
      weights.state = 0.8;
    }
    if (entities.industries.length > 0) weights.industry = 0.7;

    // Default weights
    weights.name = weights.name || 0.6;
    weights.email = weights.email || 0.5;

    return weights;
  }

  public async processQuery(query: string, contacts: Contact[]): Promise<MatchResult[]> {
    try {
      // Simple entity extraction
      const entities = this.extractSimpleEntities(query);

      // Perform search
      const results: MatchResult[] = [];

      for (const contact of contacts) {
        const match = await this.matchContact(contact, query, entities);
        if (match.score >= 0.3) {
          results.push(match);
        }
      }

      // Sort by score and limit results
      return results
        .sort((a, b) => b.score - a.score)
        .slice(0, 50);
    } catch (error) {
      console.error('Error processing query:', error);
      return [];
    }
  }

  private extractSimpleEntities(query: string): ExtractedEntities {
    const normalizedQuery = query.toLowerCase();

    return {
      companies: this.extractCompanies(normalizedQuery),
      jobTitles: this.extractJobTitles(normalizedQuery),
      locations: this.extractLocations(normalizedQuery),
      industries: this.extractIndustries(normalizedQuery),
      keywords: normalizedQuery.split(' ').filter(word => word.length > 2),
    };
  }

  private extractCompanies(query: string): string[] {
    const companies = ['loblaws', 'metro', 'sobeys', 'superstore', 'walmart', 'costco'];
    return companies.filter(company => query.includes(company));
  }

  private extractJobTitles(query: string): string[] {
    const titles = ['manager', 'director', 'category manager', 'buyer', 'specialist'];
    return titles.filter(title => query.includes(title));
  }

  private extractLocations(query: string): string[] {
    const locations = ['alberta', 'ontario', 'bc', 'toronto', 'vancouver', 'calgary', 'western canada'];
    return locations.filter(location => query.includes(location));
  }

  private extractIndustries(query: string): string[] {
    const industries = ['grocery', 'retail', 'bakery', 'food service'];
    return industries.filter(industry => query.includes(industry));
  }

  private async matchContact(contact: Contact, query: string, entities: ExtractedEntities): Promise<MatchResult> {
    let totalScore = 0;
    let matchCount = 0;
    const matchedFields: string[] = [];
    const explanations: string[] = [];

    // Check company matches
    if (contact.company && entities.companies.length > 0) {
      for (const company of entities.companies) {
        const score = fuzzyMatch(contact.company.toLowerCase(), company);
        if (score > 0.7) {
          totalScore += score;
          matchCount++;
          matchedFields.push('company');
          explanations.push(`Company: ${(score * 100).toFixed(0)}% match`);
          break;
        }
      }
    }

    // Check job title matches
    if (contact.title && entities.jobTitles.length > 0) {
      for (const title of entities.jobTitles) {
        const score = fuzzyMatch(contact.title.toLowerCase(), title);
        if (score > 0.7) {
          totalScore += score;
          matchCount++;
          matchedFields.push('title');
          explanations.push(`Title: ${(score * 100).toFixed(0)}% match`);
          break;
        }
      }
    }

    // Check location matches
    if ((contact.city || contact.state) && entities.locations.length > 0) {
      const locationText = `${contact.city || ''} ${contact.state || ''}`.toLowerCase();
      for (const location of entities.locations) {
        if (locationText.includes(location)) {
          totalScore += 0.8;
          matchCount++;
          matchedFields.push('location');
          explanations.push(`Location: matches ${location}`);
          break;
        }
      }
    }

    // Check industry matches
    if (contact.industry && entities.industries.length > 0) {
      for (const industry of entities.industries) {
        const score = fuzzyMatch(contact.industry.toLowerCase(), industry);
        if (score > 0.6) {
          totalScore += score;
          matchCount++;
          matchedFields.push('industry');
          explanations.push(`Industry: ${(score * 100).toFixed(0)}% match`);
          break;
        }
      }
    }

    // Fallback: check if any field contains query keywords
    if (matchCount === 0) {
      const allText = `${contact.name || ''} ${contact.company || ''} ${contact.title || ''} ${contact.industry || ''}`.toLowerCase();
      const queryWords = query.toLowerCase().split(' ').filter(word => word.length > 2);

      for (const word of queryWords) {
        if (allText.includes(word)) {
          totalScore += 0.5;
          matchCount++;
          matchedFields.push('general');
          explanations.push(`Contains keyword: ${word}`);
        }
      }
    }

    const finalScore = matchCount > 0 ? totalScore / matchCount : 0;
    const explanation = explanations.length > 0
      ? explanations.join(', ')
      : 'No matches found';

    return {
      contact,
      score: finalScore,
      matchedFields,
      explanation,
    };
  }
}
